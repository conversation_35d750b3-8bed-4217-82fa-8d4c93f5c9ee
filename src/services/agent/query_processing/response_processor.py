"""Response processing and validation for agent service."""

import json
import time
import uuid
from typing import Dict, Any, Optional
from loguru import logger
from ..agentic_core import AgentAction, AgentThought


class ResponseProcessor:
    """Handles LLM response processing and validation."""

    def __init__(self):
        pass

    def parse_llm_response(self, response: str, llm_client) -> Dict[str, Any]:
        """Parse and validate LLM response."""
        try:
            solution = llm_client.clean_json_response(response)

            # Handle cases where the LLM returns a plain string instead of JSON
            if isinstance(solution, str):
                logger.debug(f"LLM returned string, attempting recovery. Length: {len(solution)}")
                # Try robust recovery
                recovered = try_recover_llm_json(solution)
                if recovered is not None:
                    logger.debug("✅ Successfully recovered JSON from string response")
                    solution = recovered
                else:
                    logger.warning(
                        "LLM returned a string instead of a JSON object. Sending back to LLM for correction."
                    )
                    # Log first 500 chars for debugging
                    preview = solution[:500] + "..." if len(solution) > 500 else solution
                    logger.debug(f"Failed to parse response preview: {preview}")
                    return {
                        "analysis": {
                            "confidence": 0.1,
                            "summary": "LLM response was not valid JSON.",
                            "user_message": "Response parsing failed, trying again...",
                        },
                        "data": f"JSON_PARSE_ERROR:{response}",
                    }

            has_action = "action" in solution and solution.get("action")
            has_final_info = "current_user_task_complete" in solution and solution.get(
                "current_user_task_complete"
            )

            # Check for valid combinations
            if has_final_info:
                logger.debug("✅ LLM provided final info - query complete")
            elif has_action:
                logger.debug("✅ LLM provided action - continuing work")

            return solution

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.debug(f"JSON error at position {e.pos}: {e.msg}")

            # Log problematic section for debugging
            if hasattr(e, 'pos') and e.pos:
                start = max(0, e.pos - 50)
                end = min(len(response), e.pos + 50)
                problematic_section = response[start:end]
                logger.debug(f"Problematic JSON section: ...{problematic_section}...")

            # Try robust recovery
            recovered = try_recover_llm_json(response)
            if recovered is not None:
                logger.warning(f"✅ Recovered LLM response using try_recover_llm_json")
                return recovered

            # Try to create a fallback response for common cases
            fallback_response = self._create_fallback_response(response)
            if fallback_response:
                logger.warning(f"Using fallback response")
                return fallback_response

            # Return JSON_PARSE_ERROR instead of raising exception
            logger.warning(
                "JSON parsing failed and recovery unsuccessful. Sending back to LLM for correction."
            )
            return {
                "analysis": {
                    "confidence": 0.1,
                    "summary": "LLM response was not valid JSON.",
                    "user_message": "Response parsing failed, trying again...",
                },
                "data": f"JSON_PARSE_ERROR:{response}",
            }
        except Exception as e:
            logger.error(f"Unexpected error parsing LLM response: {e}")
            logger.debug(f"Response length: {len(response)}, type: {type(response)}")

            fallback_response = self._create_fallback_response(response)
            if fallback_response:
                logger.warning(f"Using fallback response")
                return fallback_response

            # Return JSON_PARSE_ERROR instead of raising exception
            logger.warning(
                "Unexpected error during JSON parsing. Sending back to LLM for correction."
            )
            return {
                "analysis": {
                    "confidence": 0.1,
                    "summary": "Unexpected error during LLM response parsing.",
                    "user_message": "Response parsing failed, trying again...",
                },
                "data": f"JSON_PARSE_ERROR:{response}",
            }

    def create_agent_thought(self, solution: Dict[str, Any]) -> AgentThought:
        """Create an AgentThought from LLM solution."""
        analysis_data = solution.get("analysis", {})

        return AgentThought(
            thought_id=str(uuid.uuid4()),
            analysis=analysis_data.get("summary", ""),
            user_message=analysis_data.get("user_message", ""),
            confidence=float(analysis_data.get("confidence", 0.0)),
            timestamp=time.time(),
        )

    def create_agent_action(self, action_data: Dict[str, Any]) -> AgentAction:
        """Create an AgentAction from action data."""
        tool_type = action_data.get("tool_type")

        if not tool_type:
            raise ValueError(f"Missing 'tool_type' in action_data: {action_data}")

        try:
            action = AgentAction(
                tool_type=tool_type,
                description=action_data.get("description", ""),
                command=action_data.get("parameters", {}).get("command"),
                query=action_data.get("parameters", {}).get("query"),
                parameters=action_data.get("parameters", {}),
            )
            logger.debug(
                f"✅ Successfully created AgentAction with tool_type: {action.tool_type}"
            )
            return action

        except Exception as e:
            logger.error(f"❌ Failed to create AgentAction: {e}")
            logger.error(f"   action_data: {action_data}")
            raise

    def _create_fallback_response(
        self, cleaned_response: str
    ) -> Optional[Dict[str, Any]]:
        if cleaned_response and not cleaned_response.strip().startswith("{"):
            logger.warning(
                "LLM returned text explanation instead of JSON, creating fallback completion response"
            )
            return {
                "analysis": {
                    "confidence": 0.5,
                    "summary": "LLM provided text explanation instead of JSON response",
                    "user_message": "Processing response...",
                },
                "current_user_task_complete": (
                    cleaned_response[:500] + "..."
                    if len(cleaned_response) > 500
                    else cleaned_response
                ),
            }
        return None


def try_recover_llm_json(llm_response):
    """Enhanced JSON recovery with multiple strategies."""
    import re

    # 1. Try normal JSON parse
    try:
        return json.loads(llm_response)
    except Exception:
        pass

    # 2. Try to extract the first complete {...} block with proper brace matching
    try:
        start = llm_response.find("{")
        if start != -1:
            brace_count = 0
            end = -1
            for i in range(start, len(llm_response)):
                if llm_response[i] == "{":
                    brace_count += 1
                elif llm_response[i] == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        end = i + 1
                        break

            if end != -1:
                candidate = llm_response[start:end]
                # Try to fix common JSON issues before parsing
                candidate = _fix_json_issues(candidate)
                return json.loads(candidate)
    except Exception:
        pass

    # 3. Try to find JSON between code blocks or markdown
    try:
        # Look for JSON between ```json and ``` or ``` and ```
        # Try multiple patterns for markdown blocks
        patterns = [
            r'```(?:json)?\s*(\{.*?\})\s*```',  # Complete markdown block
            r'```(?:json)?\s*(\{.*?\})\s*$',    # Markdown block without closing ```
            r'```(?:json)?\s*(\{.*?)$',         # Incomplete markdown block
        ]

        for pattern in patterns:
            match = re.search(pattern, llm_response, re.DOTALL)
            if match:
                candidate = _fix_json_issues(match.group(1))
                return json.loads(candidate)
    except Exception:
        pass

    # 4. Try to extract from the last {...} block (sometimes LLM adds explanation after)
    try:
        start = llm_response.rfind("{")
        end = llm_response.rfind("}")
        if start != -1 and end != -1 and end > start:
            candidate = llm_response[start : end + 1]
            candidate = _fix_json_issues(candidate)
            return json.loads(candidate)
    except Exception:
        pass

    # 5. Try to handle incomplete JSON by finding the last complete field
    try:
        start = llm_response.find("{")
        if start != -1:
            # Find the last complete field before any truncation
            candidate = _extract_complete_json_fields(llm_response[start:])
            if candidate:
                candidate = _fix_json_issues(candidate)
                return json.loads(candidate)
    except Exception:
        pass

    # 6. If all else fails, return None
    return None


def _fix_json_issues(json_str):
    """Fix common JSON formatting issues."""
    import re

    # Fix trailing commas
    protected = re.sub(r',(\s*[}\]])', r'\1', json_str)

    # Fix incomplete JSON by ensuring proper closing
    if protected.count('{') > protected.count('}'):
        protected += '}'

    return protected


def clean_sutra_memory_content(content: str) -> str:
    """Pass through sutra_memory content as-is since LLM provides UTF-8 content directly."""
    # No cleaning needed - LLM provides UTF-8 content like README files
    return content


def _extract_complete_json_fields(json_str):
    """Extract complete JSON fields from potentially truncated JSON."""
    import re

    # Try to find the last complete field
    # Look for pattern: "field": "value" or "field": {...}

    # Find all complete field patterns
    field_pattern = r'"[^"]+"\s*:\s*(?:"[^"]*"|[^,}]+)(?=\s*[,}])'
    matches = list(re.finditer(field_pattern, json_str))

    if not matches:
        return None

    # Build JSON with complete fields only
    complete_fields = []
    for match in matches:
        complete_fields.append(match.group(0))

    if complete_fields:
        return "{" + ", ".join(complete_fields) + "}"

    return None
