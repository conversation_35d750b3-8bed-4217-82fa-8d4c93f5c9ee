"""Refactored Agentic Service for autonomous problem solving with database integration."""

import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Iterator
from loguru import logger

from ..graph.sqlite_client import SQLiteConnection
from ..graph.incremental_indexing import IncrementalIndexing
from ..embeddings.vector_db import VectorDatabase
from .agent.agent_prompt import PromptBuilder, PromptConfiguration
from ..services.agent.agentic_core import AgentThought, AgentAction
from .llm_clients.llm_factory import llm_client_factory
from .agent.action_executor import ActionExecutor
from .agent.session_management import SessionManager
from .agent.query_processing import QueryProcessor, ResponseProcessor
from ..config import config


class AgentService:
    """Agent Service for autonomous problem solving with session persistence."""

    def __init__(self, session_id: Optional[str] = None):
        self.llm_client = llm_client_factory()
        self.db_connection = SQLiteConnection()
        self.vector_db = VectorDatabase(config.sqlite.embeddings_db)

        self.session_manager = SessionManager.get_or_create_session(session_id)
        self.response_processor = ResponseProcessor()
        self.query_processor = QueryProcessor(self.llm_client, self.response_processor)

        self.thoughts: List[AgentThought] = []
        self.session_data: List[Dict[str, Any]] = []

        # Memory-based task queue for current session
        self.remaining_queries: List[str] = []
        self.current_query_index: int = 0

        self.action_executor = ActionExecutor(self.db_connection, self.vector_db)

        # Track last action and result for dynamic prompt building
        self.last_action = None
        self.last_action_result = None

        # Track if previous response had current_user_task_complete (indicating completed task)
        # Initialize as True since first iteration is always a new task
        self.previous_task_completed = True

        # Initialize incremental indexing
        self.incremental_indexer = IncrementalIndexing(self.db_connection)

        # Determine current project name from working directory
        self.current_project_name = Path.cwd().name

    def solve_problem(
        self, problem_query: str, project_id: Optional[int] = None
    ) -> Iterator[Dict[str, Any]]:
        """
        Main entry point for the agent to solve a problem autonomously.

        Args:
            problem_query: The problem description
            project_id: Optional project context

        Yields:
            Real-time updates about the agent's thinking and actions
        """
        # Perform incremental indexing before processing the query
        yield from self._perform_incremental_indexing()

        # Set as new task for first iteration of new session
        self.previous_task_completed = True

        yield from self._handle_query(problem_query, is_new_session=True)

    def continue_conversation(
        self, query: str, project_id: Optional[int] = None
    ) -> Iterator[Dict[str, Any]]:
        """
        Continue conversation in existing session with a new query.

        Args:
            query: The new query/question
            project_id: Optional project context

        Yields:
            Real-time updates about the agent's thinking and actions
        """
        # For continuation queries, also improve them and show the improvement
        improved_query = None
        improved_tasks = None

        # Start new query in session first to get query_id
        query_id = self.session_manager.start_new_query(query)

        # Clear session data for the new query to ensure clean state
        self.session_manager.clear_session_data_for_current_query()

        # Reset last action state for new conversation
        self.last_action_result = None
        self.last_action = None

        # Set as new task for first iteration
        self.previous_task_completed = True

        # Process query improvement for continuation
        for update in self.query_processor.improve_query(query):
            yield update
            if update.get("type") == "query_improved":
                improved_query = update.get("improved_query")
                improved_tasks = update.get("tasks")
                # For continuation queries, we don't need to store tasks in conversation history
                # The LLM will manage this in Sutra memory
                pass
            elif update.get("type") == "query_improvement_failed":
                improved_query = query

        # Set up new query queue for continuation
        if improved_tasks and len(improved_tasks) > 1:
            # Multiple queries - set up queue
            self.remaining_queries = improved_tasks.copy()
            self.current_query_index = 0
        else:
            # Single query
            self.remaining_queries = [improved_query or query]
            self.current_query_index = 0

        # Perform incremental indexing before processing the continuation query
        yield from self._perform_incremental_indexing()

        yield from self._handle_query(self.remaining_queries[0], is_new_session=False)

    def _handle_query(
        self, query: str, is_new_session: bool = True
    ) -> Iterator[Dict[str, Any]]:
        """
        Handle a query (either new session or continuation).

        Args:
            query: The query to process
            project_id: Optional project context
            is_new_session: Whether this is a new session or continuation

        Yields:
            Real-time updates about the agent's thinking and actions
        """
        # Start new query in session (only for new sessions)
        if is_new_session:
            query_id = self.session_manager.start_new_query(query)
            # Clear any existing session data to ensure clean state for new session
            self.session_manager.clear_session_data_for_current_query()
        else:
            # For continuation, query_id should already be set
            query_id = self.session_manager.current_query_id

        if is_new_session:
            # Process query improvement for new sessions
            improved_query = None
            improved_tasks = None
            for update in self.query_processor.improve_query(query):
                yield update
                if update.get("type") == "query_improved":
                    improved_query = update.get("improved_query")
                    improved_tasks = update.get("tasks")
                    # Note: Query context is managed via Sutra memory
                elif update.get("type") == "query_improvement_failed":
                    improved_query = query

            # Set up query queue from improved tasks
            if improved_tasks and len(improved_tasks) > 1:
                # Multiple queries - set up queue
                self.remaining_queries = improved_tasks.copy()
                self.current_query_index = 0
                current_query = self.remaining_queries[0]

                # No need to update conversation history - LLM manages memory

                yield {
                    "type": "session_start",
                    "session_id": self.session_manager.session_id,
                    "original_problem": query,
                    "total_queries": len(self.remaining_queries),
                    "current_query_index": 1,
                    "current_query": current_query,
                    "timestamp": time.time(),
                }
            else:
                # Single query or no improvement
                current_query = (
                    improved_tasks[0] if improved_tasks else (improved_query or query)
                )
                self.remaining_queries = [current_query]
                self.current_query_index = 0

                # No need to update conversation history - LLM manages memory

                yield {
                    "type": "session_start",
                    "session_id": self.session_manager.session_id,
                    "original_problem": query,
                    "current_query": current_query,
                    "timestamp": time.time(),
                }

            # Set problem context for new session
            self.session_manager.set_problem_context(current_query)
        else:
            # For continuation, just yield query start
            current_query = query
            yield {
                "type": "query_start",
                "session_id": self.session_manager.session_id,
                "query_id": query_id,
                "query": current_query,
                "timestamp": time.time(),
                "sutra_memory_length": len(self.session_manager.get_sutra_memory()),
            }

        try:
            yield from self._solving_loop()
        except Exception as e:
            logger.error(f"❌ Agent failed: {e}")
            yield {
                "type": "error",
                "error": str(e),
                "session_id": self.session_manager.session_id,
            }

    def _build_tool_status(
        self,
        last_action_result: Optional[Dict[str, Any]],
    ) -> str:
        """
        Build tool status string from last action result.

        Args:
            last_action: Last action executed
            last_action_result: Result of last action

        Returns:
            Formatted tool status string
        """
        if not last_action_result:
            return "No previous tool execution"

        tool_type = last_action_result.get("tool_type", "unknown")
        result_data = last_action_result.get("data", "")

        # Handle different tool types
        match tool_type:
            case "semantic_search":
                return self._format_semantic_search_status(last_action_result)
            case "database":
                return self._format_database_status(last_action_result)
            case "terminal":
                return self._format_terminal_status(last_action_result)
            case "git_diff":
                return self._format_git_diff_status(last_action_result)
            case "json_parse_error":
                return "JSON parsing error occurred - retrying with error correction"
            case _:
                # Generic formatting for other tools
                if isinstance(result_data, str) and result_data:
                    return f"{tool_type} executed: {result_data[:200]}{'...' if len(result_data) > 200 else ''}"
                else:
                    return f"{tool_type} executed"

    def _format_semantic_search_status(self, action_result: Dict[str, Any]) -> str:
        """Format semantic search results for tool status."""
        query = action_result.get("query", "unknown")
        total_nodes = action_result.get("total_nodes", 0)
        code_snippet = action_result.get("code_snippet", False)

        # Build clean status summary without guidance
        status_parts = [f"Semantic search executed for query: '{query}'"]
        status_parts.append(f"Found {total_nodes} nodes")

        if code_snippet:
            status_parts.append("(with code content)")
        else:
            status_parts.append("(metadata only)")

        return " | ".join(status_parts)

    def _format_database_status(self, action_result: Dict[str, Any]) -> str:
        """Format database query results for tool status."""
        query_name = action_result.get("query_name", "unknown")
        result = action_result.get("result", "")

        # Build clean status summary without guidance
        status_parts = [f"Database query executed: {query_name}"]

        # Add result count if available
        if result and "found:" in result:
            status_parts.append(result)

        return " | ".join(status_parts)

    def _format_terminal_status(self, action_result: Dict[str, Any]) -> str:
        """Format terminal command results for tool status."""
        command = action_result.get("command", "unknown")
        success = action_result.get("success", False)
        output = action_result.get("output", "")
        status = action_result.get("status", "")

        # Handle special statuses first
        if status == "terminal_command_cancelled":
            return f"Terminal command declined by user: '{command}'"
        elif status in ["terminal_command_error", "terminal_command_timeout"]:
            return f"Terminal command {status.replace('terminal_command_', '')}: '{command}'"

        # Build clean status summary for executed commands
        if success:
            if output and output.strip():
                return f"Terminal command executed successfully: '{command}' | Found results"
            else:
                return f"Terminal command executed successfully: '{command}' | No results found"
        else:
            return f"Terminal command resulted 0 results: '{command}'"

    def _format_git_diff_status(self, action_result: Dict[str, Any]) -> str:
        """Format git diff results for tool status."""
        status = action_result.get("status", "unknown")
        data = action_result.get("data", {})

        # Build clean status summary
        status_parts = ["Git diff executed"]

        if status == "success":
            status_parts.append("Success")
            successful_changes = data.get("successful_changes", [])
            if successful_changes:
                if len(successful_changes) == 1:
                    status_parts.append(f"Modified: {successful_changes[0]}")
                else:
                    status_parts.append(f"Modified {len(successful_changes)} files")
        elif status == "error":
            status_parts.append("Failed")
        else:
            status_parts.append(f"Status: {status}")

        return " | ".join(status_parts)

    def _solving_loop(self) -> Iterator[Dict[str, Any]]:
        """Main solving loop."""
        current_iteration = 0
        json_error_retry_count = 0
        max_json_retries = 5

        while True:
            current_iteration += 1
            if current_iteration % 15 == 0:
                print(
                    f"\n⚠️  This process has been running for {current_iteration} iterations."
                )
                user_input = (
                    input("Do you want to continue? (yes/no): ").lower().strip()
                )
                if user_input != "yes":
                    yield {
                        "type": "info",
                        "message": f"Process terminated by user after {current_iteration} iterations",
                    }
                    return

            try:
                solution = self._get_agent_response(current_iteration)
            except Exception as e:
                logger.error(f"❌ Error getting agent response: {e}")
                yield {"type": "error", "error": f"Error getting agent response: {e}"}
                return

            # Check for JSON parsing errors first
            has_json_error = self._has_json_parse_error(solution)
            if has_json_error:
                json_error_retry_count += 1
                logger.warning(
                    f"JSON parsing error detected. Retry {json_error_retry_count}/{max_json_retries}"
                )

                if json_error_retry_count >= max_json_retries:
                    logger.error(
                        f"Maximum JSON retry attempts ({max_json_retries}) reached. Terminating session."
                    )
                    break

                # Simple approach: Discard malformed response and retry with same prompt
                logger.info(
                    "🔄 Discarding malformed JSON response and retrying with same prompt"
                )
                # Clear the JSON error from last_action_result to prevent infinite loop
                if (
                    self.last_action_result
                    and self.last_action_result.get("tool_type") == "json_parse_error"
                ):
                    self.last_action_result = None
                continue
            else:
                # Reset retry counter on successful parse
                json_error_retry_count = 0

            # Analyze response structure for routing
            has_final_info = "current_user_task_complete" in solution and solution.get(
                "current_user_task_complete"
            )
            has_action = "action" in solution and solution.get("action")

            # Update task completion tracking for next iteration
            # Consider task completed only when LLM explicitly says task is complete
            self.previous_task_completed = has_final_info

            if has_final_info:
                # Current query is complete
                analysis = solution.get("analysis", {})
                final_info = solution.get("current_user_task_complete", "")

                # Update Sutra memory with final findings from analysis
                if analysis.get("sutra_memory"):
                    # Extract memory update from the analysis sutra_memory field
                    # The LLM updates its memory in this field
                    new_memory = analysis.get("sutra_memory", "")
                    self.session_manager.update_sutra_memory(new_memory)

                # First yield the LLM response with analysis for CLI display
                yield {
                    "type": "llm_response",
                    "iteration": current_iteration,
                    "analysis": analysis,
                    "current_user_task_complete": final_info,
                    "timestamp": time.time(),
                }

                # Log user message and completion
                user_message = analysis.get("user_message", "Query completed")
                logger.debug(f"🤖 {user_message}")
                logger.debug(f"✅ Final Info: {final_info}")

                # Check if there are more queries to process
                self.current_query_index += 1

                if self.current_query_index < len(self.remaining_queries):
                    # More queries remaining - process next one
                    next_query = self.remaining_queries[self.current_query_index]

                    yield {
                        "type": "query_complete",
                        "current_user_task_complete": final_info,
                        "analysis": analysis,
                        "completed_query_index": self.current_query_index,
                        "timestamp": time.time(),
                    }

                    yield {
                        "type": "next_query_start",
                        "query": next_query,
                        "query_index": self.current_query_index + 1,
                        "total_queries": len(self.remaining_queries),
                        "timestamp": time.time(),
                    }

                    # Update session context for next query
                    self.session_manager.set_problem_context(next_query)
                    next_query_id = self.session_manager.start_new_query(next_query)

                    # Query context is now managed via Sutra memory

                    # Clear last action result for new task - don't send last tool data to new tasks
                    self.last_action_result = None
                    self.last_action = None

                    # Reset task completion flag for new query
                    self.previous_task_completed = True

                    # Clear session data for the current query to ensure no previous tool calls
                    self.session_manager.clear_session_data_for_current_query()

                    # Continue processing the next query
                    continue
                else:
                    # All queries completed
                    if not final_info:
                        final_info = "No final summary was provided by the LLM."
                    if not analysis.get("user_message"):
                        analysis["user_message"] = "Query completed."

                    yield {
                        "type": "all_queries_complete",
                        "current_user_task_complete": final_info,
                        "analysis": analysis,
                        "total_queries_completed": len(self.remaining_queries),
                        "session_summary": self.session_manager.get_session_summary(
                            current_iteration
                        ),
                    }
                    # End the processing
                    break

            elif has_action:
                # Need to execute an action to continue working on the query

                # First yield the LLM response with analysis for CLI display
                yield {
                    "type": "llm_response",
                    "iteration": current_iteration,
                    "analysis": solution.get("analysis", {}),
                    "action": solution.get("action", {}),
                    "timestamp": time.time(),
                }

                action = self.response_processor.create_agent_action(solution["action"])
                yield from self._process_thought_and_action(
                    solution, action, current_iteration
                )
            else:
                # No action and no final summary - this shouldn't happen
                break

    def _has_json_parse_error(self, solution: Dict[str, Any]) -> bool:
        """Check if the solution contains a JSON parsing error."""
        if not isinstance(solution, dict):
            return False

        # Check if data field contains JSON_PARSE_ERROR
        data = solution.get("data", "")
        if isinstance(data, str) and data.startswith("JSON_PARSE_ERROR:"):
            return True

        # Check if analysis indicates JSON parsing failure
        analysis = solution.get("analysis", {})
        if isinstance(analysis, dict):
            summary = analysis.get("summary", "")
            if isinstance(summary, str) and "not valid JSON" in summary:
                return True

        return False

    def _get_agent_response(self, current_iteration: int) -> Dict[str, Any]:
        """Get response from LLM."""

        # Get the current user query from remaining queries or problem context
        current_user_query = ""
        if self.remaining_queries and self.current_query_index < len(
            self.remaining_queries
        ):
            current_user_query = self.remaining_queries[self.current_query_index]
        else:
            current_user_query = self.session_manager.problem_context

        # Use the new prompt builder system
        builder = PromptBuilder()
        config = PromptConfiguration.get_config_for_context(self.last_action)

        # Build tool status from last action result
        tool_status = self._build_tool_status(self.last_action_result)

        prompt = builder.build_prompt(
            user_query=current_user_query,
            sutra_memory=self.session_manager.get_sutra_memory(),
            tool_status=tool_status,
            last_action=self.last_action,
            last_action_result=self.last_action_result,
            is_new_task=self.previous_task_completed,
            **config,
        )

        logger.debug(f"🔍 Iteration {current_iteration}: Sending prompt to LLM")

        try:
            logger.debug(f"🔍 Iteration {current_iteration}: Prompt: {prompt}")
            response = self.llm_client.call_llm(prompt)

            parsed_response = self.response_processor.parse_llm_response(
                response, self.llm_client
            )
            logger.debug(
                f"🔍 Iteration {current_iteration}: Parsed LLM response: {parsed_response}"
            )
            return parsed_response
        except Exception as e:
            logger.error(f"Failed to get valid response from LLM: {e}")

            return {
                "analysis": {
                    "confidence": 0.1,
                    "user_message": "Encountered response parsing error, attempting recovery...",
                    "summary": f"LLM response parsing failed on iteration {current_iteration}. Encountered JSON formatting issues, attempting to recover and continue.",
                },
                "current_user_task_complete": "Session terminated due to LLM response format issues - LLM had trouble providing properly formatted JSON responses",
            }

    def _process_thought_and_action(
        self, solution: Dict[str, Any], action: AgentAction, current_iteration: int
    ) -> Iterator[Dict[str, Any]]:
        """Process thought and execute action."""
        # Create thought
        thought = self.response_processor.create_agent_thought(solution)

        # Update Sutra memory with new findings from analysis
        analysis = solution.get("analysis", {})
        if analysis.get("sutra_memory"):
            # Extract memory update from the analysis sutra_memory field
            # The LLM updates its memory in this field
            new_memory = analysis.get("sutra_memory", "")
            self.session_manager.update_sutra_memory(new_memory)

        # Store thought
        self.thoughts.append(thought)

        # Yield thought directly from AgentThought object
        yield {
            "type": "thought_generated",
            "thought": {
                "id": thought.thought_id,
                "analysis": thought.analysis,
                "user_message": thought.user_message,
                "confidence": thought.confidence,
            },
        }

        # Execute action
        yield from self._execute_action(action)

    def _execute_action(self, action: AgentAction) -> Iterator[Dict[str, Any]]:
        """Execute an agent action."""
        # Clear previous terminal failure status when starting a new tool
        # This prevents "user declined", "error", or "timeout" messages from persisting when switching tools
        if (
            self.last_action_result
            and self.last_action_result.get("tool_type") == "terminal"
            and self.last_action_result.get("status")
            in [
                "terminal_command_cancelled",
                "terminal_command_error",
                "terminal_command_timeout",
            ]
            and action.tool_type.lower() != "terminal"
        ):
            self.last_action_result = None

        # Store the action for dynamic prompt building
        self.last_action = {
            "tool_type": action.tool_type,
            "parameters": action.parameters,
            "description": action.description,
        }

        yield {
            "type": "action_start",
            "action": {
                "tool_type": action.tool_type,
                "description": action.description,
                "command": action.command,
            },
        }

        tool_type = action.tool_type.lower()
        action_result = None

        if tool_type == "terminal":
            for result in self.action_executor.execute_terminal_action(action):
                yield result
                if result.get("type") == "terminal_command_executed":
                    action_result = {
                        "tool_type": "terminal",
                        "command": result["command"],
                        "success": result["success"],
                        "output": result.get("output", ""),
                        "error": result.get("error", ""),
                        "data": result.get("output", ""),
                    }
                elif result.get("type") in [
                    "terminal_command_cancelled",
                    "terminal_command_timeout",
                    "terminal_command_error",
                ]:
                    action_result = {
                        "tool_type": "terminal",
                        "command": result.get("command", "unknown"),
                        "status": result.get("type"),
                        "details": result,
                        "data": f"Command {result.get('type', 'failed')}",
                    }

        elif tool_type == "database":
            all_node_data = []
            action_result = None
            for result in self.action_executor.execute_database_action(action):
                yield result
                if result.get("type") in [
                    "database_query_node",
                    "database_query_chunk",
                ]:
                    # Collect sequential node/chunk data
                    all_node_data.append(result.get("data", ""))

                    # Set action_result immediately on first node/chunk to ensure tool status is available
                    if action_result is None:
                        action_result = {
                            "tool_type": "database",
                            "query": result.get("query"),
                            "query_name": result.get("query_name"),
                            "result": result.get("result", ""),
                            "data": result.get("data", ""),
                            "include_code": result.get("include_code", True),
                        }
                        # Store immediately for tool status
                        self.last_action_result = action_result
                elif result.get("type") == "database_query_complete":
                    # Update action_result with complete information
                    if all_node_data:
                        combined_data = "\n\n".join(all_node_data)
                        action_result = {
                            "tool_type": "database",
                            "query": result.get("query"),
                            "query_name": result.get("query_name"),
                            "result": result.get("result"),
                            "data": combined_data,
                            "include_code": result.get("include_code", True),
                        }
                    else:
                        # This handles batch mode or single results
                        action_result = {
                            "tool_type": "database",
                            "query": result.get("query"),
                            "query_name": result.get("query_name"),
                            "result": result.get("result"),
                            "data": result.get("data"),
                            "include_code": result.get("include_code", True),
                        }

                    # Store immediately for tool status
                    self.last_action_result = action_result

        elif tool_type == "semantic_search":
            all_node_data = []
            action_result = None
            for result in self.action_executor.execute_semantic_search_action(action):
                yield result
                if result.get("type") == "semantic_search_node":
                    # Collect sequential node data
                    all_node_data.append(result.get("data", ""))

                    # Set action_result immediately on first node to ensure tool status is available
                    if action_result is None:
                        action_result = {
                            "tool_type": "semantic_search",
                            "query": result.get("query"),
                            "result": f"result found: {result.get('total_nodes', 0)}",
                            "data": result.get("data", ""),
                            "total_nodes": result.get("total_nodes", 0),
                            "code_snippet": result.get("code_snippet", False),
                        }
                        # Store immediately for tool status
                        self.last_action_result = action_result
                elif result.get("type") == "semantic_search_batch":
                    # Handle batch delivery from delivery queue
                    action_result = {
                        "tool_type": "semantic_search",
                        "query": result.get("query"),
                        "result": result.get("result"),
                        "data": result.get("data", ""),
                        "total_nodes": result.get("total_nodes", 0),
                        "code_snippet": result.get("code_snippet", False),
                        "batch_info": result.get("batch_info", {}),
                    }
                    # Store immediately for tool status
                    self.last_action_result = action_result
                elif result.get("type") == "semantic_search_complete":
                    # Update action_result with complete information
                    if all_node_data:
                        combined_data = "\n\n".join(all_node_data)
                        action_result = {
                            "tool_type": "semantic_search",
                            "query": result.get("query"),
                            "result": result.get("result"),
                            "data": combined_data,
                            "total_nodes": result.get("total_nodes", 0),
                            "code_snippet": result.get("code_snippet", False),
                        }
                    else:
                        # This handles batch mode (metadata only) or zero results
                        action_result = {
                            "tool_type": "semantic_search",
                            "query": result.get("query"),
                            "result": result.get("result"),
                            "data": result.get("data"),
                            "total_nodes": result.get("total_nodes", 0),
                            "code_snippet": result.get("code_snippet", False),
                        }
                    # Store immediately for tool status
                    self.last_action_result = action_result

        elif tool_type == "git_diff":
            for result in self.action_executor.execute_git_diff_action(action):
                yield result
                if result.get("tool_type") == "git_diff":
                    action_result = {
                        "tool_type": "git_diff",
                        "status": result.get("status", "unknown"),
                        "data": result.get("data", {}),
                    }

        else:
            yield {
                "type": "action_error",
                "error": f"Unknown action type: {tool_type}",
            }

        # Store action result
        if action_result:
            self.session_data.append(
                {
                    "type": "action_result",
                    "id": str(time.time()),
                    "timestamp": time.time(),
                    "action": {
                        "tool_type": action.tool_type,
                        "description": action.description,
                    },
                    "data": action_result,
                }
            )

        # Store the action result for dynamic prompt building
        # For semantic search and database, the result is already set during processing
        if tool_type not in ["semantic_search", "database"] and action_result:
            self.last_action_result = action_result

    def get_session_info(self) -> Dict[str, Any]:
        """Get information about the current session."""
        return self.session_manager.get_conversation_summary()

    def clear_session(self) -> None:
        """Clear the current session."""
        self.session_manager.clear_session()

    @classmethod
    def list_sessions(cls) -> List[Dict[str, Any]]:
        """List all available sessions."""
        return SessionManager.list_sessions()

    @classmethod
    def get_session(cls, session_id: str) -> Optional["AgentService"]:
        """Get an existing session by ID."""
        try:
            return cls(session_id=session_id)
        except Exception as e:
            logger.error(f"Failed to load session {session_id}: {e}")
            return None

    def _perform_incremental_indexing(self) -> Iterator[Dict[str, Any]]:
        """
        Perform incremental indexing to update the database with latest code changes.

        Yields:
            Updates about the incremental indexing process
        """
        try:
            # Check if the project exists in the database
            if not self.db_connection.project_exists(self.current_project_name):
                logger.debug(
                    f"Project '{self.current_project_name}' not found in database, skipping incremental indexing"
                )
                return

            logger.debug(
                f"🔄 Checking for code changes in project: {self.current_project_name}"
            )

            # Perform incremental indexing
            result = self.incremental_indexer.reindex_database(
                project_name=self.current_project_name, create_indexes=True
            )

            if result.get("status") == "success":
                # Check if there were any changes
                files_changed = result.get("files_changed", 0)
                files_added = result.get("files_added", 0)
                files_deleted = result.get("files_deleted", 0)

                total_changes = files_changed + files_added + files_deleted

                if total_changes > 0:
                    yield {
                        "type": "incremental_indexing_complete",
                        "message": f"Database updated with latest changes",
                        "details": {
                            "files_changed": files_changed,
                            "files_added": files_added,
                            "files_deleted": files_deleted,
                            "nodes_added": result.get("nodes_added", 0),
                            "nodes_deleted": result.get("nodes_deleted", 0),
                            "relationships_added": result.get("relationships_added", 0),
                            "relationships_deleted": result.get(
                                "relationships_deleted", 0
                            ),
                        },
                    }
                    logger.debug(
                        f"✅ Database updated: {files_changed} changed, {files_added} added, {files_deleted} deleted files"
                    )
                else:
                    logger.debug("No code changes detected, database is up to date")
            else:
                error_msg = result.get(
                    "error", "Unknown error during incremental indexing"
                )
                logger.warning(f"Incremental indexing failed: {error_msg}")
                yield {
                    "type": "warning",
                    "message": f"Failed to update database with latest changes: {error_msg}",
                }

        except Exception as e:
            logger.error(f"Error during incremental indexing: {e}")
            yield {
                "type": "warning",
                "message": f"Could not check for code changes: {str(e)}",
            }

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        if hasattr(self, "db_connection"):
            self.db_connection.__exit__(exc_type, exc_val, exc_tb)
        if hasattr(self, "vector_db"):
            self.vector_db.__exit__(exc_type, exc_val, exc_tb)
