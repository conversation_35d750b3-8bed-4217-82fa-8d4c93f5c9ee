// Test API service file
const express = require('express');
const app = express();

// Dependencies
const config = require('./config');
const logger = require('./logger');

// Middleware
app.use(express.json());

// Routes
app.get('/api/data', async (req, res) => {
  try {
    const data = await fetchData();
    res.json(data);
  } catch (error) {
    logger.error('Error fetching data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

async function fetchData() {
  const response = await fetch('/external-api');
  return response.data;
}

// Helper functions
function processData(data) {
  return data.map(item => item.value);
}

function validateInput(input) {
  return input && typeof input === 'object';
}

module.exports = app;
