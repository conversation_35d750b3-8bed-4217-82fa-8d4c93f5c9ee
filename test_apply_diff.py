#!/usr/bin/env python3
"""Test applying git diff to actual files"""

import subprocess
import tempfile
from pathlib import Path

def test_git_apply():
    """Test git apply with our multi-file diff"""
    
    # Create test diff
    test_diff = """--- a/test_api.js
+++ b/test_api.js
@@ -6,2 +6,4 @@
 const config = require('./config');
 const logger = require('./logger');
+const validator = require('./validator');
+const cache = require('./cache');

@@ -24,2 +26,4 @@
   const response = await fetch('/external-api');
   return response.data;
+  // Add validation
+  validator.validate(response.data);
 }

@@ -29,2 +33,6 @@
 function processData(data) {
-  return data.map(item => item.value);
+  return data
+    .filter(item => validator.isValid(item))
+    .map(item => ({
+      ...item,
+      processed: true
+    }));
 }"""

    print("🧪 Testing Git Apply with Real Files")
    print("=" * 50)
    
    try:
        # Read original content
        original_content = Path('test_api.js').read_text()
        print(f"📄 Original file length: {len(original_content)} chars")
        
        # Create temp directory for git apply test
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Copy original file
            test_file = temp_path / "test_api.js"
            test_file.write_text(original_content)
            
            # Write diff to file
            diff_file = temp_path / "changes.diff"
            diff_file.write_text(test_diff)
            
            print(f"📁 Created temp files in: {temp_dir}")
            
            # Try git apply
            result = subprocess.run(
                ["git", "apply", "--no-index", str(diff_file)],
                cwd=temp_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("✅ Git apply succeeded!")
                modified_content = test_file.read_text()
                print(f"📄 Modified file length: {len(modified_content)} chars")
                
                # Show changes
                print("\n📝 Changes applied:")
                if "validator" in modified_content:
                    print("  ✅ Added validator import")
                if "cache" in modified_content:
                    print("  ✅ Added cache import")
                if "validator.validate" in modified_content:
                    print("  ✅ Added validation call")
                if ".filter(" in modified_content:
                    print("  ✅ Modified processData function")
                    
                return True
            else:
                print(f"❌ Git apply failed: {result.stderr}")
                print(f"📄 Stdout: {result.stdout}")
                return False
                
    except Exception as e:
        print(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_parsing():
    """Test manual diff parsing approach"""
    
    print("\n🔧 Testing Manual Diff Parsing")
    print("=" * 50)
    
    # Simple diff for testing
    simple_diff = """@@ -6,2 +6,4 @@
 const config = require('./config');
 const logger = require('./logger');
+const validator = require('./validator');
+const cache = require('./cache');"""

    try:
        # Read original content
        original_content = Path('test_api.js').read_text()
        lines = original_content.splitlines(keepends=True)
        
        print(f"📄 Original lines: {len(lines)}")
        
        # Parse the hunk header
        hunk_line = "@@ -6,2 +6,4 @@"
        parts = hunk_line.split()
        old_range = parts[1][1:]  # Remove '-'
        old_start = int(old_range.split(",")[0]) - 1  # Convert to 0-based
        
        print(f"📍 Starting at line: {old_start + 1}")
        
        # Apply changes manually
        # Insert new lines after line 7 (logger import)
        new_lines = [
            "const validator = require('./validator');\n",
            "const cache = require('./cache');\n"
        ]
        
        # Insert at position 7 (after logger import)
        for i, new_line in enumerate(new_lines):
            lines.insert(7 + i, new_line)
        
        modified_content = "".join(lines)
        print(f"📄 Modified lines: {len(lines)}")
        
        # Verify changes
        if "validator" in modified_content and "cache" in modified_content:
            print("✅ Manual parsing succeeded!")
            return True
        else:
            print("❌ Manual parsing failed!")
            return False
            
    except Exception as e:
        print(f"💥 Manual parsing failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Git Diff Application Testing")
    print("=" * 60)
    
    success = True
    success &= test_git_apply()
    success &= test_manual_parsing()
    
    if success:
        print("\n🎉 All diff application tests passed!")
    else:
        print("\n❌ Some tests failed!")
