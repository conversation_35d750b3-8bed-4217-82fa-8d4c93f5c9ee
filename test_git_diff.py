#!/usr/bin/env python3
"""Test script for git diff functionality"""

import os
import sys
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_multi_file_diff():
    """Test multi-file git diff parsing"""
    
    # Multi-file diff example
    multi_diff = """--- a/test_api.js
+++ b/test_api.js
@@ -5,2 +5,4 @@
 const config = require('./config');
 const logger = require('./logger');
+const validator = require('./validator');
+const cache = require('./cache');

@@ -20,2 +22,4 @@
   return response.data;
+  // Add validation
+  validator.validate(response.data);
 }

@@ -28,2 +32,6 @@
 function processData(data) {
-  return data.map(item => item.value);
+  return data
+    .filter(item => validator.isValid(item))
+    .map(item => ({
+      ...item,
+      processed: true
+    }));
 }

--- a/test_config.js
+++ b/test_config.js
@@ -2,2 +2,6 @@
 module.exports = {
   port: 3000,
-  database: 'localhost'
+  database: process.env.DATABASE_URL || 'localhost',
+  cache: {
+    ttl: 300,
+    maxSize: 1000
+  }
 };"""

    print("🧪 Testing Multi-File Git Diff Parsing")
    print("=" * 50)
    
    # Test splitting logic
    lines = multi_diff.splitlines()
    file_paths = []
    
    for line in lines:
        if line.startswith("+++ b/"):
            path = line[6:].strip()
            if path != "/dev/null" and path not in file_paths:
                file_paths.append(path)
                print(f"📁 Found file: {path}")
    
    print(f"\n✅ Found {len(file_paths)} files in diff")
    
    # Test chunk splitting
    current_chunk = []
    current_file = None
    diff_chunks = []
    
    for line in lines:
        if line.startswith("--- a/"):
            # Start of new file diff
            if current_chunk and current_file:
                diff_chunks.append("\n".join(current_chunk))
            current_chunk = [line]
            current_file = line[6:].strip()
        else:
            current_chunk.append(line)
    
    # Add the last chunk
    if current_chunk and current_file:
        diff_chunks.append("\n".join(current_chunk))
    
    print(f"✅ Split into {len(diff_chunks)} chunks")
    
    for i, chunk in enumerate(diff_chunks):
        print(f"\n📄 Chunk {i+1}:")
        print("-" * 30)
        print(chunk[:200] + "..." if len(chunk) > 200 else chunk)
    
    return True

def test_utf8_encoding():
    """Test UTF-8 encoding in diff"""
    
    utf8_diff = """--- a/test_utf8.js
+++ b/test_utf8.js
@@ -1,3 +1,5 @@
 // Test file with UTF-8 content
 const message = "Hello World";
+const emoji = "🚀 Rocket";
+const unicode = "Café ñoño";
 console.log(message);"""

    print("\n🌍 Testing UTF-8 Encoding")
    print("=" * 50)
    
    # Test encoding
    try:
        encoded = utf8_diff.encode('utf-8')
        decoded = encoded.decode('utf-8')
        print("✅ UTF-8 encoding/decoding successful")
        print(f"📊 Original length: {len(utf8_diff)}")
        print(f"📊 Encoded length: {len(encoded)} bytes")
        print(f"📊 Decoded length: {len(decoded)}")
        
        # Test special characters
        if "🚀" in decoded and "ñ" in decoded:
            print("✅ Special characters preserved")
        else:
            print("❌ Special characters lost")
            
    except Exception as e:
        print(f"❌ UTF-8 test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🔧 Git Diff Tool Testing")
    print("=" * 60)
    
    success = True
    
    try:
        success &= test_multi_file_diff()
        success &= test_utf8_encoding()
        
        if success:
            print("\n🎉 All tests passed!")
        else:
            print("\n❌ Some tests failed!")
            
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")
        import traceback
        traceback.print_exc()
