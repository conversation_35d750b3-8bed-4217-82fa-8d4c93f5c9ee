#!/usr/bin/env python3
"""Test proper git diff format"""

import subprocess
import tempfile
from pathlib import Path

def test_proper_git_diff():
    """Test with proper git diff format"""
    
    # Proper single-file diff
    proper_diff = """--- a/test_api.js
+++ b/test_api.js
@@ -6,2 +6,4 @@
 const config = require('./config');
 const logger = require('./logger');
+const validator = require('./validator');
+const cache = require('./cache');
 
@@ -23,3 +25,5 @@
 async function fetchData() {
   const response = await fetch('/external-api');
   return response.data;
+  // Add validation
+  validator.validate(response.data);
 }
 
@@ -29,2 +33,6 @@
 function processData(data) {
-  return data.map(item => item.value);
+  return data
+    .filter(item => validator.isValid(item))
+    .map(item => ({
+      ...item,
+      processed: true
+    }));
 }"""

    print("🧪 Testing Proper Git Diff Format")
    print("=" * 50)
    
    try:
        # Read original content
        original_content = Path('test_api.js').read_text()
        print(f"📄 Original file length: {len(original_content)} chars")
        
        # Create temp directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Copy original file
            test_file = temp_path / "test_api.js"
            test_file.write_text(original_content)
            
            # Write diff to file
            diff_file = temp_path / "changes.diff"
            diff_file.write_text(proper_diff)
            
            print(f"📁 Testing in: {temp_dir}")
            
            # Try git apply
            result = subprocess.run(
                ["git", "apply", "--no-index", str(diff_file)],
                cwd=temp_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("✅ Git apply succeeded!")
                modified_content = test_file.read_text()
                print(f"📄 Modified file length: {len(modified_content)} chars")
                
                # Show what was added
                added_lines = []
                for line in modified_content.splitlines():
                    if "validator" in line or "cache" in line or "processed: true" in line:
                        added_lines.append(line.strip())
                
                print(f"📝 Added {len(added_lines)} new lines:")
                for line in added_lines[:5]:  # Show first 5
                    print(f"  + {line}")
                    
                return True
            else:
                print(f"❌ Git apply failed:")
                print(f"  Return code: {result.returncode}")
                print(f"  Stderr: {result.stderr}")
                print(f"  Stdout: {result.stdout}")
                return False
                
    except Exception as e:
        print(f"💥 Test failed: {e}")
        return False

def test_json_escaping():
    """Test JSON escaping for the diff"""
    
    print("\n🔧 Testing JSON Escaping")
    print("=" * 50)
    
    # Test the diff as it would appear in JSON
    json_diff = """--- a/test_api.js\\n+++ b/test_api.js\\n@@ -6,2 +6,4 @@\\n const config = require('./config');\\n const logger = require('./logger');\\n+const validator = require('./validator');\\n+const cache = require('./cache');\\n"""
    
    try:
        # Unescape the JSON
        unescaped = json_diff.replace('\\n', '\n').replace('\\"', '"')
        print("✅ JSON unescaping successful")
        print(f"📊 Original length: {len(json_diff)}")
        print(f"📊 Unescaped length: {len(unescaped)}")
        
        # Check if it looks like a proper diff
        if unescaped.startswith("--- a/") and "+++ b/" in unescaped and "@@" in unescaped:
            print("✅ Proper diff format detected")
            return True
        else:
            print("❌ Invalid diff format")
            return False
            
    except Exception as e:
        print(f"❌ JSON escaping test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Proper Git Diff Testing")
    print("=" * 60)
    
    success = True
    success &= test_proper_git_diff()
    success &= test_json_escaping()
    
    if success:
        print("\n🎉 All proper diff tests passed!")
    else:
        print("\n❌ Some tests failed!")
